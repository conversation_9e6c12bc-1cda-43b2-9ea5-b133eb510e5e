{"targets": ["swap", "swap2", "previous"], "passes": [{"name": "ntsc_encode", "intarget": "minecraft:main", "outtarget": "swap"}, {"name": "ntsc_decode", "intarget": "swap", "outtarget": "swap2", "auxtargets": [{"name": "BaseSampler", "id": "minecraft:main"}]}, {"name": "color_convolve", "intarget": "swap2", "outtarget": "swap"}, {"name": "deconverge", "intarget": "swap", "outtarget": "minecraft:main"}, {"name": "blur", "intarget": "minecraft:main", "outtarget": "swap", "uniforms": [{"name": "BlurDir", "values": [1.0, 0.0]}, {"name": "<PERSON><PERSON>", "values": [10.0]}]}, {"name": "blur", "intarget": "swap", "outtarget": "minecraft:main", "uniforms": [{"name": "BlurDir", "values": [0.0, 1.0]}, {"name": "<PERSON><PERSON>", "values": [10.0]}]}, {"name": "scan_pincushion", "intarget": "minecraft:main", "outtarget": "swap"}, {"name": "phosphor", "intarget": "swap", "outtarget": "minecraft:main", "auxtargets": [{"name": "PrevSampler", "id": "previous"}], "uniforms": [{"name": "Phosphor", "values": [0.4, 0.4, 0.4]}]}, {"name": "blit", "intarget": "minecraft:main", "outtarget": "previous"}]}