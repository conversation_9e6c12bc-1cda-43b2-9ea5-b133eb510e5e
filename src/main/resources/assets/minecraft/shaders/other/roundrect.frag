#version 120

uniform vec2 uSize;
uniform float uRadius;
uniform vec4 uColor;
uniform vec4 uCorners;

float roundedBoxSDF(vec2 p, vec2 halfSize, float r) {
    vec2 d = abs(p) - halfSize;
    return length(max(d, 0.0)) + min(max(d.x, d.y), 0.0) - r;
}

void main() {
    vec2 p = gl_TexCoord[0].st * uSize - uSize * 0.5;

    vec2 signP = sign(p);

    float rLocal;
    if (signP.x < 0.0 && signP.y > 0.0) {
        // top-left
        rLocal = uCorners.x * uRadius;
    } else if (signP.x > 0.0 && signP.y > 0.0) {
        // top-right
        rLocal = uCorners.y * uRadius;
    } else if (signP.x > 0.0 && signP.y < 0.0) {
        // bottom-right
        rLocal = uCorners.z * uRadius;
    } else {
        // bottom-left
        rLocal = uCorners.w * uRadius;
    }

    vec2 halfSize = uSize * 0.5 - vec2(rLocal);

    float sdf = roundedBoxSDF(p, halfSize, rLocal);

    float aa = fwidth(sdf);
    float alpha = smoothstep(0.0, aa, -sdf);

    gl_FragColor = vec4(uColor.rgb, uColor.a * alpha);
}
