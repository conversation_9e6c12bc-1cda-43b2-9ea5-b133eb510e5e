{"elements": [{"from": [0, 0, 0], "to": [16, 16, 16], "faces": {"down": {"uv": [0, 0, 16, 16], "texture": "#bottom", "cullface": "down"}, "up": {"uv": [0, 0, 16, 16], "texture": "#top", "cullface": "up", "tintindex": 0}, "north": {"uv": [0, 0, 16, 16], "texture": "#side", "cullface": "north"}, "south": {"uv": [0, 0, 16, 16], "texture": "#side", "cullface": "south"}, "west": {"uv": [0, 0, 16, 16], "texture": "#side", "cullface": "west"}, "east": {"uv": [0, 0, 16, 16], "texture": "#side", "cullface": "east"}}}, {"from": [0, 0, 0], "to": [16, 16, 16], "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#overlay", "tintindex": 0, "cullface": "north"}, "south": {"uv": [0, 0, 16, 16], "texture": "#overlay", "tintindex": 0, "cullface": "south"}, "west": {"uv": [0, 0, 16, 16], "texture": "#overlay", "tintindex": 0, "cullface": "west"}, "east": {"uv": [0, 0, 16, 16], "texture": "#overlay", "tintindex": 0, "cullface": "east"}}}]}